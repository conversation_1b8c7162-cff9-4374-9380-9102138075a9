import { Extension } from '@tiptap/core'
import { React<PERSON>enderer } from '@tiptap/react'
import Suggestion from '@tiptap/suggestion'
import { PluginKey } from 'prosemirror-state'
import tippy from 'tippy.js'
import {
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Code,
  Table,
  Image,
  Minus,
  FileText,
  BarChart3,
  Link,
  Calendar,
  CheckSquare,
  Columns,
  Columns2,
  Columns3,
  Columns4,
  ListTree
} from 'lucide-react'
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState
} from 'react'

interface CommandItem {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  command: ({ editor, range }: any) => void
  keywords?: string[]
}

const commands: CommandItem[] = [
  {
    title: 'Heading 1',
    description: 'Big section heading',
    icon: Heading1,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setNode('heading', { level: 1 })
        .run()
    },
    keywords: ['h1', 'heading', 'title']
  },
  {
    title: 'Heading 2',
    description: 'Medium section heading',
    icon: Heading2,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setNode('heading', { level: 2 })
        .run()
    },
    keywords: ['h2', 'heading', 'subtitle']
  },
  {
    title: 'Heading 3',
    description: 'Small section heading',
    icon: Heading3,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setNode('heading', { level: 3 })
        .run()
    },
    keywords: ['h3', 'heading']
  },
  {
    title: 'Bullet List',
    description: 'Create a simple bullet list',
    icon: List,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleBulletList()
        .run()
    },
    keywords: ['ul', 'list', 'bullet']
  },
  {
    title: 'Numbered List',
    description: 'Create a numbered list',
    icon: ListOrdered,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleOrderedList()
        .run()
    },
    keywords: ['ol', 'list', 'numbered', 'ordered']
  },
  {
    title: 'Quote',
    description: 'Capture a quote',
    icon: Quote,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleBlockquote()
        .run()
    },
    keywords: ['quote', 'blockquote']
  },
  {
    title: 'Code Block',
    description: 'Create a code block',
    icon: Code,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleCodeBlock()
        .run()
    },
    keywords: ['code', 'codeblock']
  },
  {
    title: 'Table',
    description: 'Insert a table',
    icon: Table,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run()
    },
    keywords: ['table']
  },
  {
    title: 'Horizontal Rule',
    description: 'Insert a horizontal divider',
    icon: Minus,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setHorizontalRule()
        .run()
    },
    keywords: ['hr', 'rule', 'divider', 'separator']
  },
  {
    title: 'Image',
    description: 'Insert an image',
    icon: Image,
    command: ({ editor, range }) => {
      const url = window.prompt('Enter image URL:')
      if (url) {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .setImage({ src: url })
          .run()
      }
    },
    keywords: ['image', 'img', 'picture', 'photo']
  },
  {
    title: 'Chart',
    description: 'Insert a chart',
    icon: BarChart3,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent({
          type: 'chart',
          attrs: {
            type: 'bar',
            data: {
              labels: ['A', 'B', 'C'],
              datasets: [{
                label: 'Sample Data',
                data: [10, 20, 30]
              }]
            }
          }
        })
        .run()
    },
    keywords: ['chart', 'graph', 'visualization']
  },
  {
    title: 'Citation',
    description: 'Insert a citation',
    icon: FileText,
    command: ({ editor, range }) => {
      const citationId = window.prompt('Enter citation ID:')
      if (citationId) {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .insertContent({
            type: 'citation',
            attrs: {
              citationId: citationId
            }
          })
          .run()
      }
    },
    keywords: ['citation', 'reference', 'cite']
  },
  {
    title: 'Task List',
    description: 'Create a task list',
    icon: CheckSquare,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .toggleTaskList()
        .run()
    },
    keywords: ['task', 'todo', 'checklist', 'checkbox']
  },
  {
    title: 'References',
    description: 'Insert references section',
    icon: FileText,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent({
          type: 'references',
          attrs: {
            id: 'references',
          },
        })
        .run()
    },
    keywords: ['references', 'citations', 'bibliography', 'sources']
  },
  {
    title: 'Math Expression',
    description: 'Insert mathematical expression',
    icon: ({ className }: { className?: string }) => <span className={`text-lg ${className || ''}`}>∑</span>,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setInlineMath('')
        .run()
    },
    keywords: ['math', 'mathematics', 'equation', 'formula', 'latex']
  },
  {
    title: 'Details',
    description: 'Insert collapsible details section',
    icon: ({ className }: { className?: string }) => <span className={`text-lg ${className || ''}`}>▶</span>,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .setDetails()
        .run()
    },
    keywords: ['details', 'collapsible', 'disclosure', 'expand', 'collapse']
  },
  {
    title: 'Table of Contents',
    description: 'Insert table of contents',
    icon: ListTree,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertContent({
          type: 'tableOfContents',
          attrs: {
            id: 'table-of-contents',
          },
        })
        .run()
    },
    keywords: ['toc', 'table', 'contents', 'outline', 'index']
  },
  {
    title: '2 Columns',
    description: 'Insert 2 equal columns',
    icon: Columns2,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertColumns('equal-2')
        .run()
    },
    keywords: ['columns', '2col', 'layout', 'equal']
  },
  {
    title: '3 Columns',
    description: 'Insert 3 equal columns',
    icon: Columns3,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertColumns('equal-3')
        .run()
    },
    keywords: ['columns', '3col', 'layout', 'equal']
  },
  {
    title: '4 Columns',
    description: 'Insert 4 equal columns',
    icon: Columns4,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertColumns('equal-4')
        .run()
    },
    keywords: ['columns', '4col', 'layout', 'equal']
  },
  {
    title: '1/3 - 2/3 Columns',
    description: 'Insert 1/3 - 2/3 ratio columns',
    icon: Columns,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertColumns('ratio-1-2')
        .run()
    },
    keywords: ['columns', 'ratio', 'layout', '1/3', '2/3']
  },
  {
    title: '2/3 - 1/3 Columns',
    description: 'Insert 2/3 - 1/3 ratio columns',
    icon: Columns,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertColumns('ratio-2-1')
        .run()
    },
    keywords: ['columns', 'ratio', 'layout', '2/3', '1/3']
  },
  {
    title: 'Centered Column',
    description: 'Insert centered 2/3 width column',
    icon: Columns,
    command: ({ editor, range }) => {
      editor
        .chain()
        .focus()
        .deleteRange(range)
        .insertColumns('centered')
        .run()
    },
    keywords: ['columns', 'centered', 'layout', 'single']
  },
  {
    title: 'AI Commands',
    description: 'Access AI-powered writing tools',
    icon: ({ className }: { className?: string }) => <span className={`text-lg ${className || ''}`}>🤖</span>,
    command: ({ editor, range }) => {
      // Delete the slash command text and insert "/ai" in a single chain execution
      editor.chain().focus().deleteRange(range).insertContent('/ai').run()
    },
    keywords: ['ai', 'artificial', 'intelligence', 'improve', 'grammar', 'writing']
  }
]

interface CommandListProps {
  items: CommandItem[]
  command: (item: CommandItem) => void
}

interface CommandListRef {
  onKeyDown: (props: { event: KeyboardEvent }) => boolean
}

const CommandList = React.memo(forwardRef<CommandListRef, CommandListProps>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const selectItem = React.useCallback((index: number) => {
    const item = props.items[index]
    if (item) {
      props.command(item)
    }
  }, [props.items, props.command])

  const upHandler = React.useCallback(() => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length)
  }, [selectedIndex, props.items.length])

  const downHandler = React.useCallback(() => {
    setSelectedIndex((selectedIndex + 1) % props.items.length)
  }, [selectedIndex, props.items.length])

  const enterHandler = React.useCallback(() => {
    selectItem(selectedIndex)
  }, [selectItem, selectedIndex])

  useEffect(() => setSelectedIndex(0), [props.items])

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === 'ArrowUp') {
        upHandler()
        return true
      }

      if (event.key === 'ArrowDown') {
        downHandler()
        return true
      }

      if (event.key === 'Enter') {
        enterHandler()
        return true
      }

      return false
    },
  }))

  return (
    <div className="z-50 min-w-[300px] max-w-[400px] bg-background border border-border rounded-lg shadow-lg overflow-hidden" data-testid="slash-command-menu">
      <div className="p-2 border-b bg-muted/50">
        <p className="text-xs text-muted-foreground font-medium">
          Commands
        </p>
      </div>
      <div className="max-h-[300px] overflow-y-auto">
        {props.items.length ? (
          props.items.map((item, index) => (
            <button
              key={index}
              className={`w-full flex items-center gap-3 p-3 text-left hover:bg-muted/50 transition-colors ${
                index === selectedIndex ? 'bg-muted' : ''
              }`}
              onClick={() => selectItem(index)}
            >
              <div className="flex-shrink-0">
                <item.icon className="w-4 h-4 text-muted-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-foreground">
                  {item.title}
                </div>
                <div className="text-xs text-muted-foreground">
                  {item.description}
                </div>
              </div>
            </button>
          ))
        ) : (
          <div className="p-3 text-sm text-muted-foreground">
            No results found
          </div>
        )}
      </div>
    </div>
  )
}))

CommandList.displayName = 'CommandList'

export const slashCommandsSuggestion = {
  items: ({ query }: { query: string }) => {
    return commands
      .filter(item => {
        const searchTerm = query.toLowerCase()
        return (
          item.title.toLowerCase().includes(searchTerm) ||
          item.description.toLowerCase().includes(searchTerm) ||
          (item.keywords && item.keywords.some(keyword =>
            keyword.toLowerCase().includes(searchTerm)
          ))
        )
      })
      .slice(0, 10)
  },

  render: () => {
    let component: ReactRenderer
    let popup: any

    return {
      onStart: (props: any) => {
        component = new ReactRenderer(CommandList, {
          props,
          editor: props.editor,
        })

        if (!props.clientRect) {
          return
        }

        popup = tippy('body', {
          getReferenceClientRect: props.clientRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'bottom-start',
        })
      },

      onUpdate(props: any) {
        component.updateProps(props)

        if (!props.clientRect) {
          return
        }

        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        })
      },

      onKeyDown(props: any) {
        if (props.event.key === 'Escape') {
          popup[0].hide()
          return true
        }

        return (component.ref as CommandListRef)?.onKeyDown(props)
      },

      onExit() {
        popup[0].destroy()
        component.destroy()
      },
    }
  },
}

export const SlashCommands = Extension.create({
  name: 'slashCommands',

  addOptions() {
    return {
      suggestion: {
        char: '/',
        command: ({ editor, range, props }: any) => {
          props.command({ editor, range })
        },
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        ...this.options.suggestion,
        pluginKey: new PluginKey('slashCommands'),
      }),
    ]
  },
})
